# 长银定价利率改造最终影响分析

## 概述

基于对实际代码调用的深入分析，梳理长银定价利率变化对各个环节的真实影响。

## 1. 关键发现：Flow模块中的固定利率使用

### 1.1 CycfcRepayPlanCalc中的固定利率（❌ 需要改造）

**位置**: `src/main/java/com/maguo/loan/cash/flow/util/CycfcRepayPlanCalc.java`

#### 1.1.1 当前使用固定利率的代码
```java
public static List<RepayPlanItem> calcGuaranteePlan(BigDecimal loanAmt, int periods) {
    //bank合同利率 - 使用固定15%
    List<RepayPlan> bankRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankRate(), periods);
    //bank对客利率 - 使用固定23.99%
    List<RepayPlan> bankCustomRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankCustomRate(), periods);
    
    // 融担费 = 对客还款额 - 对资还款额
    BigDecimal guaranteeAmt = bankCustomRepayPlan.getPrincipal().add(bankCustomRepayPlan.getInterest())
        .subtract(bankRepayPlan.getPrincipal()).subtract(bankRepayPlan.getInterest());
}
```

#### 1.1.2 调用链路分析
```
CommonRepayPlanCalc.calcRepayPlan() → CycfcRepayPlanCalc.calcConsultPlan() → calcGuaranteePlan()
```

#### 1.1.3 实际调用情况
**需要进一步确认**：CommonRepayPlanCalc是否被实际业务流程调用

### 1.2 Rate36Service中的固定利率（❌ 未被调用）

**位置**: `src/main/java/com/maguo/loan/cash/flow/service/rate/Rate36Service.java`

```java
public BigDecimal trialClearGuaranteeAmt(Loan loan, Integer period, List<RepayPlan> repayPlans, LocalDate date) {
    QhBank qhBank = QhBank.getQhBankBy(loan.getBankChannel());
    BigDecimal guaranteeRate = qhBank.getBankCustomRate().subtract(qhBank.getBankRate());
    // 使用固定利率：23.99% - 15% = 8.99%
}
```

**结论**: 该方法未被调用，无需改造

## 2. Capital模块中的利率使用（✅ 自动生效）

### 2.1 实际被调用的融担费计算

#### 2.1.1 CYBKLoanService.getGuaranteeAmtAndRate()
- **调用位置**: `CYBKCustDailyGuaranteeHandler.java` 第98行
- **用途**: 生成对客日终担保费文件
- **利率使用**: `credit.getBankRate()` - 会自动使用动态利率 ✅

#### 2.1.2 CYBKLoanService.fillGuaranteeAmt()
- **调用位置**: 还款计划查询时填充融担费
- **利率使用**: 基于动态利率计算的还款计划 ✅

#### 2.1.3 CYBKRepayService.getGuaranteeAmt()
- **调用位置**: 提前结清融担费计算
- **利率使用**: `loan.getBankRate()` - 会自动使用动态利率 ✅

### 2.2 罚息计算

#### 2.2.1 Capital模块对资罚息
**位置**: `CYBKReccAbstractHandler.java` 第203行

```java
// 罚息日利率
BigDecimal penaltyRateDay = loan.getBankRate().divide(new BigDecimal("360"), SIX, RoundingMode.HALF_UP);
// 对资罚息 = 逾期金额 * 罚息日利率 * 逾期天数
```

**影响**: 会自动使用动态定价利率计算对资罚息 ✅

#### 2.2.2 Flow模块对客罚息
**位置**: `RepayPlanOverdueDueBatch.java` 第260行

```java
private BigDecimal totalAmtPenaltyAmt(Loan loan, RepayPlan repayPlan, long overDay) {
    BigDecimal consultOverdueRate = CONSULT_OVERDUE_RATE; // 固定0.0985%
    return totalAmtBase.multiply(new BigDecimal(overDay)).multiply(consultOverdueRate.divide(PERCENT));
}
```

**影响**: 使用固定罚息率，不受对资利率影响 ✅

## 3. 需要重点关注的改造点

### 3.1 需要确认是否被调用的地方

#### 3.1.1 CycfcRepayPlanCalc.calcGuaranteePlan()
- **问题**: 使用固定的QhBank.CYBK.getBankRate() (15%)
- **影响**: 如果被调用，融担费计算会不准确
- **改造**: 需要传入动态利率参数

#### 3.1.2 CommonRepayPlanCalc.calcRepayPlan()
- **调用**: 最终调用CycfcRepayPlanCalc.calcConsultPlan()
- **问题**: 间接使用固定利率
- **需要确认**: 是否有业务场景调用此方法

### 3.2 确认无需改造的地方

#### 3.2.1 Flow模块咨询费计算
- **LVXINLoanService.planConsultFee()**: 使用固定月费率1%，不受对资利率影响 ✅
- **CYBKLoanService.planConsultFee()**: 使用固定月费率1%，不受对资利率影响 ✅

#### 3.2.2 Flow模块罚息计算
- **RepayPlanOverdueDueBatch**: 使用固定罚息率0.0985%，不受对资利率影响 ✅

## 4. 两种利率体系的影响分析

### 4.1 当前两种利率体系

#### 4.1.1 Flow模块配置
- **QhBank.CYBK**: bankCustomRate=23.99%, bankRate=15%
- **RateLevel**: RATE_24=23.99%, RATE_36=35.99%

#### 4.1.2 Capital模块配置
- **BankChannel.CYBK**: irrRate=23.99%

### 4.2 对资利率变化的影响

假设长银定价利率从23.99%降到18%：

#### 4.2.1 Capital模块（✅ 自动调整）
- **融担费**: (23.99% - 18%) = 5.99% → 融担费增加
- **对资罚息**: 使用18%计算 → 对资罚息减少
- **对账文件**: 自动使用新利率

#### 4.2.2 Flow模块（❓ 需要确认）
- **如果CycfcRepayPlanCalc被调用**: 仍使用固定15% → 计算错误
- **咨询费**: 不受影响（固定1%月费率）
- **对客罚息**: 不受影响（固定0.0985%日费率）

## 5. 改造建议

### 5.1 立即需要确认的问题

1. **CycfcRepayPlanCalc.calcConsultPlan()是否被实际调用？**
2. **CommonRepayPlanCalc.calcRepayPlan()是否被实际调用？**
3. **Flow模块是否有其他地方使用QhBank.getBankRate()？**

### 5.2 如果被调用，需要改造的方案

#### 5.2.1 CycfcRepayPlanCalc改造
```java
// 改造前
public static List<RepayPlanItem> calcGuaranteePlan(BigDecimal loanAmt, int periods) {
    List<RepayPlan> bankRateRepayPlans = calcBasicPlan(loanAmt, QhBank.CYBK.getBankRate(), periods);
}

// 改造后
public static List<RepayPlanItem> calcGuaranteePlan(BigDecimal loanAmt, int periods, BigDecimal dynamicBankRate) {
    List<RepayPlan> bankRateRepayPlans = calcBasicPlan(loanAmt, dynamicBankRate, periods);
}
```

#### 5.2.2 调用方改造
需要在调用时传入动态的对资利率，而不是使用固定配置

## 6. 总结

### 6.1 确定需要改造的地方

1. **Capital模块**: 授信结果查询接口添加定价利率字段处理 ✅
2. **Flow模块**: 需要确认CycfcRepayPlanCalc是否被调用，如被调用需要改造

### 6.2 确定无需改造的地方

1. **Capital模块所有融担费计算**: 已使用动态利率 ✅
2. **Capital模块对资罚息计算**: 已使用动态利率 ✅
3. **Flow模块咨询费和对客罚息**: 使用固定费率，不受影响 ✅

### 6.3 关键风险点

**如果Flow模块的CycfcRepayPlanCalc被实际使用**，那么融担费计算会出现错误，因为它使用的是固定15%而不是动态定价利率。

**建议**: 优先确认CycfcRepayPlanCalc的实际调用情况！
