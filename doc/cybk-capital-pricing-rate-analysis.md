# 长银资方定价利率改造分析报告（Capital模块）

## 概述

长银资方要求利率要根据用信接口返回的定价利率参数进行计算，之前是固定使用23.99%作为对资利率，现在需要用接口返回的定价利率进行动态计算。

## 1. 之前对资利率来源分析

### 1.1 Capital模块中的利率设置来源

#### 1.1.1 BankChannel枚举中的固定配置

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/enums/BankChannel.java`

```java
/**
 * 长银银行 直连
 */
CYBK("CYBK", "长银直连", new BigDecimal("0.2399")), // 23.99%
```

- `irrRate`: **23.99%** - **这是之前固定对资利率的来源** ⭐

#### 1.1.2 授信时利率设置

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/service/CommonService.java`

```java
public Credit creditApply(CreditApplyVo<ExtInfoVo> apply) {
    // ...
    credit.setBankRate(bankChannel.getIrrRate()); // 设置为23.99%
    credit = creditRepository.save(credit);
    // ...
}
```

#### 1.1.3 利率传递链路（Capital模块）

```
BankChannel.CYBK.irrRate(23.99%) → CommonService.creditApply() → Credit.bankRate → Loan.bankRate
```

## 2. 各环节利率使用详细分析

### 2.1 授信环节

#### 2.1.1 之前的做法
- **利率来源**: BankChannel枚举固定配置23.99%
- **设置位置**: `CommonService.creditApply()`
- **具体代码**:
  ```java
  credit.setBankRate(bankChannel.getIrrRate()); // 固定23.99%
  ```

#### 2.1.2 现在需要改为
- **利率来源**: 长银额度查询接口返回的定价利率
- **接口字段**: `CYBKLimitQueryResponse.priceIntRate`
- **改造点**: 授信成功后调用额度查询接口获取定价利率并更新Credit.bankRate

### 2.2 用信环节

#### 2.2.1 之前的做法
- **利率来源**: 从Credit实体的bankRate字段获取（固定23.99%）
- **设置位置**: `CYBKLoanService.buildApplyRequest()`
- **具体代码**:
  ```java
  request.setPriceIntRat(loan.getBankRate()); // 使用固定的23.99%
  ```

#### 2.2.2 现在需要改为
- **利率来源**: 从Credit实体获取动态定价利率
- **改造点**: 使用授信阶段获取的定价利率

### 2.3 还款计划生成环节

#### 2.3.1 之前的做法
- **对资利率计算**: 使用固定的bankRate(23.99%)
- **具体位置**: `CoreRepayPlanCalculator.calculate()`
- **代码示例**:
  ```java
  List<RepayPlan> bankPlanList = PlanGenerator.genPlan(
      InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, 
      repayDate, loanAmt, bankRate, periods); // bankRate = 23.99%
  ```

#### 2.3.2 现在需要改为
- **对资利率计算**: 使用动态定价利率
- **改造点**: bankRate字段存储动态定价利率

### 2.4 LPR查询环节

#### 2.4.1 之前的做法
- **利率使用**: `CYBKLoanService.lpr()`
- **具体代码**:
  ```java
  lprQueryRequest.setPriceRate(credit.getBankRate()); // 固定23.99%
  // 资方利率(单位百分比)
  resultVo.setBusinessRate(credit.getBankRate().multiply(BigDecimal.valueOf(PERCENT)).toString());
  ```

#### 2.4.2 现在需要改为
- **利率使用**: 使用动态定价利率
- **改造点**: credit.getBankRate()返回动态定价利率

### 2.5 担保费计算环节

#### 2.5.1 之前的做法
- **担保费计算**: `CYBKCreditService.getGuaranteeAmtAndRate()`
- **具体代码**:
  ```java
  // 计算对资利率的等额本息还款金额
  List<RepayPlan> repayBankPlanList = PlanGenerator.genPlan(
      InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST, 
      LocalDate.now(), credit.getLoanAmt(), credit.getBankRate(), credit.getPeriods());
  // credit.getBankRate() = 23.99%
  ```

#### 2.5.2 现在需要改为
- **担保费计算**: 使用动态定价利率
- **改造点**: credit.getBankRate()返回动态定价利率

## 3. 长银接口中的定价利率字段

### 3.1 额度查询接口响应中的定价利率

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/limit/CYBKLimitQueryResponse.java`

```java
/** 长银定价利率，16位数字，9位小数，表示年化利率 */
private BigDecimal priceIntRate; ⭐ 这是长银返回的动态定价利率

/** 对客展示利率，16位数字，9位小数 */
private BigDecimal custShowRate;
```

### 3.2 用信申请请求中的利率字段

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/loan/CYBKLoanApplyRequest.java`

```java
/**
 * 利率 - 需要设置为动态定价利率
 */
private BigDecimal priceIntRat;

/**
 * 客户展示利率
 */
private BigDecimal custDayRate;
```

## 4. 具体改造点对比

### 4.1 授信流程改造

#### 4.1.1 之前的流程
```
授信申请 → 设置固定利率(23.99%) → 存储到Credit.bankRate
```

#### 4.1.2 改造后的流程
```
授信申请 → 授信成功 → 调用额度查询接口 → 获取priceIntRate → 更新Credit.bankRate
```

**改造位置**: 
- `CYBKCreditService.bankCreditQuery()` - 授信查询成功后调用额度查询
- `CYBKCreditService.doCreditResultCallback()` - 授信回调成功后调用额度查询

### 4.2 用信流程改造

#### 4.2.1 之前的流程
```java
// CYBKLoanService.buildApplyRequest()
request.setPriceIntRat(loan.getBankRate()); // 固定23.99%
```

#### 4.2.2 改造后的流程
```java
// CYBKLoanService.buildApplyRequest()
Credit credit = creditRepository.findById(loan.getCreditId()).orElseThrow();
request.setPriceIntRat(credit.getBankRate()); // 动态定价利率
```

### 4.3 数据传递改造

#### 4.3.1 之前的数据传递
```java
// CommonService.loanApply()
Loan loan = CreditLoanConvert.INSTANCE.creditToLoan(credit);
// loan.bankRate 继承 credit.bankRate (固定23.99%)
```

#### 4.3.2 改造后的数据传递
```java
// CommonService.loanApply()
Loan loan = CreditLoanConvert.INSTANCE.creditToLoan(credit);
// loan.bankRate 继承 credit.bankRate (动态定价利率)
```

## 5. 需要修改的关键代码位置

### 5.1 授信相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `CommonService.creditApply()` | 初始化授信 | `credit.setBankRate(bankChannel.getIrrRate())` | 保持不变，后续更新 |
| `CYBKCreditService.bankCreditQuery()` | 授信查询成功处理 | 无额度查询 | 调用额度查询获取定价利率 |
| `CYBKCreditService.doCreditResultCallback()` | 授信回调处理 | 无额度查询 | 调用额度查询获取定价利率 |

### 5.2 用信相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `CYBKLoanService.buildApplyRequest()` | 构建用信请求 | `request.setPriceIntRat(loan.getBankRate())` | 使用Credit中的动态定价利率 |

### 5.3 利率计算相关

| 文件位置 | 方法 | 当前逻辑 | 改造后逻辑 |
|---------|------|---------|-----------|
| `CoreRepayPlanCalculator.calculate()` | 还款计划计算 | 使用固定bankRate | 使用动态定价利率 |
| `CYBKCreditService.getGuaranteeAmtAndRate()` | 担保费计算 | 使用固定利率 | 使用动态利率 |
| `CYBKLoanService.lpr()` | LPR查询 | 使用固定利率 | 使用动态利率 |

## 6. 改造实施方案

### 6.1 获取定价利率的时机

1. **授信成功后**: 从额度查询接口(`CYBKLimitQueryResponse`)获取`priceIntRate`
2. **更新Credit**: 将定价利率更新到Credit.bankRate字段
3. **用信申请时**: 使用Credit中的动态定价利率

### 6.2 数据存储策略

- **Credit表**: 使用现有的`bankRate`字段存储动态定价利率
- **Loan表**: 通过CreditLoanConvert继承Credit的bankRate
- **向后兼容**: 历史数据保持原有的23.99%利率不变

### 6.3 实施步骤

1. **第一步**: 修改授信成功处理，调用额度查询获取定价利率
2. **第二步**: 验证用信流程使用动态定价利率
3. **第三步**: 验证利率计算相关逻辑
4. **第四步**: 全面测试验证

## 7. 总结

本次改造主要是将Capital模块中长银资方的固定利率(23.99%)改为动态获取的定价利率，关键改造点：

- **利率来源变化**: BankChannel固定23.99% → 长银额度查询接口动态获取
- **核心改造**: 授信成功后调用额度查询接口获取定价利率并更新Credit.bankRate
- **数据流转**: 保持现有的Credit → Loan的数据传递链路不变
- **向后兼容**: 确保历史数据和异常情况的处理
