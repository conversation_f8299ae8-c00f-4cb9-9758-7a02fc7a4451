# 长银资方定价利率改造分析报告（实际调用分析）

## 概述

长银资方要求利率要根据用信接口返回的定价利率参数进行计算，之前是固定使用23.99%作为对资利率，现在需要用接口返回的定价利率进行动态计算。

## 1. 实际被调用的利率相关代码分析

### 1.1 Capital模块中的利率设置来源

#### 1.1.1 BankChannel枚举中的固定配置

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/enums/BankChannel.java`

```java
/**
 * 长银银行 直连
 */
CYBK("CYBK", "长银直连", new BigDecimal("0.2399")), // 23.99%
```

- `irrRate`: **23.99%** - **这是之前固定对资利率的来源** ⭐

#### 1.1.2 授信时利率设置（实际被调用）

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/service/CommonService.java`

```java
public Credit creditApply(CreditApplyVo<ExtInfoVo> apply) {
    // ...
    credit.setBankRate(bankChannel.getIrrRate()); // 设置为23.99%
    credit = creditRepository.save(credit);
    // ...
}
```

**调用链路**: `CYBKCreditService.apply()` → `CommonService.creditApply()` → 设置固定23.99%

#### 1.1.3 利率传递链路（Capital模块）

```
BankChannel.CYBK.irrRate(23.99%) → Credit.bankRate → Loan.bankRate → 各种实际计算
```

## 2. 实际被调用的各环节利率使用分析

### 2.1 授信环节（✅ 实际被调用）

#### 2.1.1 之前的做法
- **调用入口**: `CYBKCreditService.apply()` → `CommonService.creditApply()`
- **利率来源**: BankChannel枚举固定配置23.99%
- **具体代码**:
  ```java
  credit.setBankRate(bankChannel.getIrrRate()); // 固定23.99%
  ```

#### 2.1.2 现在需要改为
- **利率来源**: 长银额度查询接口返回的定价利率
- **接口字段**: `CYBKLimitQueryResponse.priceIntRate`
- **改造点**: 授信成功后调用额度查询接口获取定价利率并更新Credit.bankRate

### 2.2 用信环节（✅ 实际被调用）

#### 2.2.1 之前的做法
- **调用入口**: `CYBKLoanService.bankLoanApply()` → `buildApplyRequest()`
- **利率来源**: 从Loan实体的bankRate字段获取（固定23.99%）
- **具体代码**:
  ```java
  // CYBKLoanService.buildApplyRequest() - 第189行
  request.setPriceIntRat(loan.getBankRate()); // 使用固定的23.99%
  request.setCustDayRate(loan.getCustomRate()); // 对客利率
  ```

#### 2.2.2 现在需要改为
- **利率来源**: 使用动态定价利率
- **改造点**: loan.getBankRate()返回动态定价利率

### 2.3 LPR查询环节（✅ 实际被调用）

#### 2.3.1 之前的做法
- **调用入口**: `ManageService.lprQuery()` → `CYBKLoanService.lpr()`
- **具体代码**:
  ```java
  // CYBKLoanService.lpr() - 第575行
  lprQueryRequest.setPriceRate(credit.getBankRate()); // 固定23.99%
  // 资方利率(单位百分比)
  resultVo.setBusinessRate(credit.getBankRate().multiply(BigDecimal.valueOf(PERCENT)).toString());
  ```

#### 2.3.2 现在需要改为
- **利率使用**: 使用动态定价利率
- **改造点**: credit.getBankRate()返回动态定价利率

### 2.4 融担费计算环节（✅ 实际被调用）

#### 2.4.1 之前的做法
- **调用入口**: `CYBKLoanService.fillGuaranteeAmt()` - 第617行
- **具体代码**:
  ```java
  // 计算对客利率的等额本息还款金额
  List<RepayPlan> repayPlanList = PlanGenerator.genPlan(
      InterestType.AVERAGE_PRINCIPAL_PLUS_INTEREST,
      LocalDate.now(), loan.getLoanAmt(), credit.getCustomRate(), credit.getPeriods());
  // 融担费 = 对客还款额 - 对资还款额
  p.setGuaranteeAmt(repayPlan.getPrincipal().add(repayPlan.getInterest())
      .subtract(p.getPrincipalAmt()).subtract(p.getInterestAmt()));
  ```

#### 2.4.2 现在需要改为
- **改造点**: 这里使用的是对客利率计算，对资利率在p.getPrincipalAmt()和p.getInterestAmt()中体现
- **注意**: 需要确保p.getPrincipalAmt()和p.getInterestAmt()使用动态定价利率计算

### 2.5 还款试算环节（✅ 实际被调用）

#### 2.5.1 之前的做法
- **调用入口**: `CYBKRepayService.trialClear()` → 长银还款试算接口
- **具体代码**:
  ```java
  // CYBKRepayService.trialClear() - 第878行
  CYBKRepayTrialResponse resp = cybkRequestService.repayTrial(req, loan.getGuaranteeCompany());
  ```

#### 2.5.2 现在需要改为
- **改造点**: 长银还款试算接口会根据之前用信时传递的利率进行计算
- **注意**: 需要确保用信时传递的是动态定价利率

## 3. 长银接口中的定价利率字段

### 3.1 额度查询接口响应中的定价利率

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/limit/CYBKLimitQueryResponse.java`

```java
/** 长银定价利率，16位数字，9位小数，表示年化利率 */
private BigDecimal priceIntRate; ⭐ 这是长银返回的动态定价利率

/** 对客展示利率，16位数字，9位小数 */
private BigDecimal custShowRate;
```

### 3.2 用信申请请求中的利率字段

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/loan/CYBKLoanApplyRequest.java`

```java
/**
 * 利率 - 需要设置为动态定价利率
 */
private BigDecimal priceIntRat;

/**
 * 客户展示利率
 */
private BigDecimal custDayRate;
```

## 4. 实际需要改造的代码位置

### 4.1 授信成功后获取定价利率（🔧 需要新增）

#### 4.1.1 改造位置
- `CYBKCreditService.bankCreditQuery()` - 授信查询成功后调用额度查询
- `CYBKCreditService.doCreditResultCallback()` - 授信回调成功后调用额度查询

#### 4.1.2 改造内容
```java
// 授信成功后调用额度查询接口获取定价利率
if (CreditStatus.SUCCESS.equals(creditResultVo.getStatus())) {
    // 调用额度查询接口
    CYBKLimitQueryResponse limitResponse = requestService.limitQuery(limitRequest, credit.getGuaranteeCompany());
    if (limitResponse != null && limitResponse.getPriceIntRate() != null) {
        // 更新Credit的bankRate为动态定价利率
        credit.setBankRate(limitResponse.getPriceIntRate());
        creditRepository.save(credit);
    }
}
```

### 4.2 用信申请使用动态利率（✅ 已有代码，无需改造）

#### 4.2.1 当前代码
```java
// CYBKLoanService.buildApplyRequest() - 第189行
request.setPriceIntRat(loan.getBankRate()); // 会自动使用动态利率
```

#### 4.2.2 说明
- 用信时loan.getBankRate()会从Credit继承，如果Credit.bankRate已更新为动态利率，这里无需改造

### 4.3 LPR查询使用动态利率（✅ 已有代码，无需改造）

#### 4.3.1 当前代码
```java
// CYBKLoanService.lpr() - 第575行
lprQueryRequest.setPriceRate(credit.getBankRate()); // 会自动使用动态利率
```

#### 4.3.2 说明
- 如果Credit.bankRate已更新为动态利率，这里无需改造

### 4.4 还款计划查询（✅ 已有代码，无需改造）

#### 4.4.1 当前代码
```java
// CYBKLoanService.getBankPlan() - 第526行
// 长银还款计划通过接口查询，不需要本地计算
CYBKRepayPlanQueryResponse response = requestService.repayPlanQuery(request, loan.getGuaranteeCompany());
```

#### 4.4.2 说明
- 长银还款计划是通过接口查询的，不是本地计算，无需改造

## 5. 实际改造工作量评估

### 5.1 需要改造的地方（🔧 实际需要修改）

| 序号 | 文件位置 | 方法 | 改造内容 | 工作量 |
|------|---------|------|---------|--------|
| 1 | `CYBKCreditService.bankCreditQuery()` | 授信查询成功处理 | 新增调用额度查询获取定价利率 | 中等 |
| 2 | `CYBKCreditService.doCreditResultCallback()` | 授信回调处理 | 新增调用额度查询获取定价利率 | 中等 |

### 5.2 无需改造的地方（✅ 自动生效）

| 序号 | 文件位置 | 方法 | 说明 |
|------|---------|------|------|
| 1 | `CYBKLoanService.buildApplyRequest()` | 用信申请 | loan.getBankRate()会自动使用动态利率 |
| 2 | `CYBKLoanService.lpr()` | LPR查询 | credit.getBankRate()会自动使用动态利率 |
| 3 | `CYBKLoanService.fillGuaranteeAmt()` | 融担费计算 | 会自动使用动态利率计算的还款计划 |
| 4 | `CYBKLoanService.getBankPlan()` | 还款计划查询 | 通过接口查询，不是本地计算 |
| 5 | `CYBKRepayService.trialClear()` | 还款试算 | 通过接口试算，使用用信时的利率 |

### 5.3 不存在或未被调用的地方（❌ 无需关注）

| 序号 | 文件位置 | 说明 |
|------|---------|------|
| 1 | `CoreRepayPlanCalculator.calculate()` | 长银不使用本地还款计划计算 |
| 2 | `CYBKCreditService.getGuaranteeAmtAndRate()` | 该方法不存在或未被调用 |

## 6. 具体改造方案

### 6.1 核心改造点

**只需要在授信成功后调用额度查询接口获取定价利率并更新Credit.bankRate**

### 6.2 改造代码示例

```java
// 在CYBKCreditService.bankCreditQuery()中添加
if (CreditStatus.SUCCESS.equals(creditResultVo.getStatus())) {
    try {
        // 调用额度查询接口获取定价利率
        CYBKLimitQueryRequest limitRequest = new CYBKLimitQueryRequest();
        limitRequest.setApplCde(credit.getCreditNo());
        CYBKLimitQueryResponse limitResponse = requestService.limitQuery(limitRequest, credit.getGuaranteeCompany());

        if (limitResponse != null && limitResponse.getPriceIntRate() != null) {
            // 更新Credit的bankRate为动态定价利率
            credit.setBankRate(limitResponse.getPriceIntRate());
            creditRepository.save(credit);
            logger.info("长银授信成功，更新定价利率: creditId={}, priceIntRate={}",
                credit.getId(), limitResponse.getPriceIntRate());
        }
    } catch (Exception e) {
        logger.error("获取长银定价利率失败，使用默认利率: creditId={}", credit.getId(), e);
        // 异常情况下保持原有的固定利率，确保业务不中断
    }
}
```

### 6.3 数据流转验证

```
1. 授信成功 → 调用额度查询 → 获取priceIntRate → 更新Credit.bankRate
2. 用信申请 → loan.getBankRate() → 自动使用动态利率
3. LPR查询 → credit.getBankRate() → 自动使用动态利率
4. 融担费计算 → 基于动态利率的还款计划 → 自动生效
```

## 7. 风险控制

### 7.1 异常处理
- 当额度查询接口失败时，保持使用固定23.99%利率，确保业务不中断
- 添加详细的日志记录，便于问题排查

### 7.2 向后兼容
- 历史数据保持原有的23.99%利率不变
- 新授信的客户使用动态定价利率

### 7.3 测试重点
- 授信成功后定价利率的获取和存储
- 用信申请时动态利率的使用
- LPR查询时动态利率的使用
- 异常情况下的降级处理

## 8. 总结

**实际改造工作量很小**：
- **主要改造**: 只需要在授信成功后调用额度查询接口获取定价利率
- **自动生效**: 其他环节会自动使用更新后的动态利率
- **风险可控**: 异常情况下可降级使用固定利率

这是一个**低风险、小改动、大收益**的改造方案。
