# 长银资方定价利率改造分析报告

## 概述

长银资方要求利率要根据用信接口返回的定价利率参数进行计算，之前是固定使用23.99%作为对资利率，现在需要用接口返回的定价利率进行动态计算。

## 1. 当前固定利率使用情况

### 1.1 QhBank枚举中的固定配置

**位置**: `src/main/java/com/maguo/loan/cash/flow/enums/QhBank.java`

```java
CYBK(new BigDecimal("0.239900"), new BigDecimal("0.1500"), BankChannel.CYBK, BindSignMode.SHARE, false, "长银消金", 5),
```

- `bankCustomRate`: 23.99% - 银行对客户的利率
- `bankRate`: 15% - 银行合同利率（对资利率）

### 1.2 RateLevel枚举中的固定利率

**位置**: `src/main/java/com/maguo/loan/cash/flow/enums/RateLevel.java`

```java
RATE_24(new BigDecimal("0.2399")),  // 23.99%
RATE_36(new BigDecimal("0.3599")),  // 35.99%
```

## 2. 长银接口中的定价利率字段

### 2.1 额度查询接口响应中的定价利率

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/limit/CYBKLimitQueryResponse.java`

```java
/** 长银定价利率，16位数字，9位小数，表示年化利率 */
private BigDecimal priceIntRate;

/** 对客展示利率，16位数字，9位小数 */
private BigDecimal custShowRate;
```

### 2.2 用信申请请求中的利率字段

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/dto/loan/CYBKLoanApplyRequest.java`

```java
/**
 * 利率
 */
private BigDecimal priceIntRat;

/**
 * 客户展示利率
 */
private BigDecimal custDayRate;
```

## 3. 需要修改的关键位置

### 3.1 借据实体中的利率字段

**位置**: `src/main/java/com/maguo/loan/cash/flow/entity/Loan.java`

```java
/**
 * 对客利率（金融）
 */
private BigDecimal irrRate;

/**
 * 资方利率
 */
private BigDecimal bankRate;
```

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/entity/Loan.java`

```java
/**
 * 对客费率
 */
private BigDecimal customRate;

/**
 * 对资费率
 */
private BigDecimal bankRate;
```

### 3.2 利率计算相关服务

**位置**: `src/main/java/com/maguo/loan/cash/flow/service/rate/Rate36Service.java`

- 融担费费率计算：`qhBank.getBankCustomRate().subtract(qhBank.getBankRate())`
- 试算结清咨询费计算中使用了固定的银行利率

### 3.3 长银LPR查询服务

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKLoanService.java`

```java
@Override
public LoanLprResultVo lpr(Credit credit) {
    CYBKLprQueryRequest lprQueryRequest = new CYBKLprQueryRequest();
    lprQueryRequest.setSignDate(credit.getPassTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
    lprQueryRequest.setPriceRate(credit.getBankRate()); // 使用固定的bankRate
    // ...
}
```

## 4. 改造方案

### 4.1 获取定价利率的时机

1. **授信成功后**: 从额度查询接口(`CYBKLimitQueryResponse`)获取`priceIntRate`
2. **用信申请时**: 使用获取到的定价利率设置到`CYBKLoanApplyRequest.priceIntRat`
3. **用信成功后**: 将定价利率存储到借据表的`bankRate`字段

### 4.2 需要修改的核心流程

#### 4.2.1 授信流程修改

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKCreditService.java`

- 授信成功后调用额度查询接口获取定价利率
- 将定价利率存储到Credit实体中

#### 4.2.2 用信流程修改

**位置**: `capital-core/src/main/java/com/jinghang/capital/core/banks/cybk/service/CYBKLoanService.java`

- 用信申请时从Credit中获取定价利率
- 设置到用信请求的`priceIntRat`字段
- 用信成功后将定价利率存储到Loan实体的`bankRate`字段

#### 4.2.3 利率计算服务修改

**位置**: `src/main/java/com/maguo/loan/cash/flow/service/rate/Rate36Service.java`

- 修改融担费计算逻辑，使用动态的定价利率
- 修改试算结清咨询费计算逻辑

### 4.3 数据库字段调整

可能需要在以下表中添加字段来存储定价利率：

1. **Credit表**: 添加`pricing_rate`字段存储授信时的定价利率
2. **Loan表**: 使用现有的`bank_rate`字段存储用信时的定价利率

## 5. 影响范围评估

### 5.1 直接影响

1. **长银授信流程**: 需要获取和存储定价利率
2. **长银用信流程**: 需要使用动态定价利率
3. **利率计算逻辑**: 所有使用固定15%利率的地方需要改为动态获取
4. **还款计划生成**: 可能影响利息计算
5. **罚息计算**: 影响逾期罚息的计算

### 5.2 间接影响

1. **对账文件**: 可能需要调整利率相关字段
2. **报表统计**: 涉及利率的统计报表需要验证
3. **历史数据**: 需要考虑历史借据的利率处理

## 6. 实施建议

### 6.1 实施步骤

1. **第一步**: 修改授信流程，获取并存储定价利率
2. **第二步**: 修改用信流程，使用动态定价利率
3. **第三步**: 修改利率计算相关服务
4. **第四步**: 全面测试验证

### 6.2 风险控制

1. **向后兼容**: 确保历史数据的利率计算不受影响
2. **异常处理**: 当无法获取定价利率时的降级策略
3. **数据一致性**: 确保各个环节使用的利率保持一致

### 6.3 测试重点

1. **授信流程**: 验证定价利率的获取和存储
2. **用信流程**: 验证定价利率的使用
3. **利率计算**: 验证各种利率计算场景
4. **边界情况**: 验证异常情况的处理

## 7. 总结

本次改造主要是将长银资方的固定利率改为动态获取的定价利率，涉及授信、用信、利率计算等多个核心流程。需要仔细处理各个环节的数据传递和存储，确保利率的一致性和准确性。
