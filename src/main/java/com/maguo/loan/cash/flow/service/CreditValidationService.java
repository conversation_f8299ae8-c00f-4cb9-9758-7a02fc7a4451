package com.maguo.loan.cash.flow.service;

import com.jinghang.cash.api.dto.ProjectInfoDto;
import com.maguo.loan.cash.flow.entrance.common.service.ProjectInfoService;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.concurrent.TimeUnit;

/**
 * 授信阶段公共参数校验服务
 * 包含授信黑暗期、日授信限额、授信锁定期限、年龄范围、可提现范围等校验
 */
@Service
public class CreditValidationService {

    private static final Logger logger = LoggerFactory.getLogger(CreditValidationService.class);

    private static final String PREFIX_CREDIT = "CREDIT";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final long LOCK_WAIT_SECOND = 5L;
    private static final long LOCK_RELEASE_SECOND = 10L;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private CacheService cacheService;
    @Autowired
    private LockService lockService;
    @Autowired
    private PreOrderRepository preOrderRepository;

    /**
     * 校验授信黑暗期
     * * @return 是否在黑暗期内
     */
    public boolean isInCreditDarkHours(ProjectInfoDto projectElements) {
        try {
            String creditDarkHours = projectElements.getElements().getCreditDarkHours();
            if (StringUtils.isBlank(creditDarkHours)) {
                logger.info("项目未配置授信黑暗期，projectCode: {}", projectElements.getProjectCode());
                return true;
            }
            return checkTimeInRange(creditDarkHours, "授信黑暗期");
        } catch (Exception e) {
            logger.error("校验授信黑暗期失败，projectCode: {}", projectElements.getProjectCode(), e);
            return false;
        }
    }

    /**
     * 校验日授信限额
     *
     * @param applyAmount 申请金额
     * @return 是否超限
     */
    public boolean isDailyCreditLimitExceeded(ProjectInfoDto projectElements, BigDecimal applyAmount) {
        try {
            BigDecimal dailyCreditLimit = projectElements.getElements().getDailyCreditLimit();
            if (dailyCreditLimit == null) {
                logger.info("项目未配置日授信限额，projectCode: {}", projectElements.getProjectCode());
                return false;
            }

            String baseKey = buildCreditLimitRedisKey(projectElements);
            String lockKey = baseKey + ":lock";
            String businessId = "credit-check-" + projectElements.getProjectCode();
            performDailyCreditCheck(baseKey, lockKey, dailyCreditLimit, applyAmount, businessId, projectElements);
            return false;
        } catch (Exception e) {
            logger.error("校验日授信限额失败，projectCode: {}", projectElements.getProjectCode(), e);
            return false;
        }
    }

    /**
     * 校验授信锁定期限
     *
     * @param projectCode    项目编码
     * @param lastCreditTime 上次授信时间
     * @return 是否在锁定期内
     */
    public boolean isInCreditLockPeriod(String projectCode, LocalDateTime lastCreditTime) {
        if (StringUtils.isBlank(projectCode) || lastCreditTime == null) {
            logger.warn("项目编码或上次授信时间为空，无法校验授信锁定期限");
            return false;
        }

        try {
            ProjectInfoDto projectElements = projectInfoService.queryProjectInfo(projectCode);
            if (projectElements == null) {
                logger.warn("未找到项目要素配置，projectCode: {}", projectCode);
                return false;
            }

            Integer creditLockDays = projectElements.getElements().getCreditLockDays();
            if (creditLockDays == null || creditLockDays <= 0) {
                logger.info("项目未配置授信锁定期限或配置无效，projectCode: {}", projectCode);
                return false;
            }

            LocalDateTime lockEndTime = lastCreditTime.plusDays(creditLockDays);
            boolean inLockPeriod = LocalDateTime.now().isBefore(lockEndTime);

            if (inLockPeriod) {
                logger.info("用户在授信锁定期内，projectCode: {}, 上次授信时间: {}, 锁定期: {}天",
                    projectCode, lastCreditTime, creditLockDays);
            }

            return inLockPeriod;
        } catch (Exception e) {
            logger.error("校验授信锁定期限失败，projectCode: {}", projectCode, e);
            return false;
        }
    }

    /**
     * 校验年龄范围
     *
     * @param age         年龄
     * @return 是否在允许范围内
     */
    public boolean isAgeInRange(ProjectInfoDto projectElements, Integer age) {
        try {

            String ageRange = projectElements.getElements().getAgeRange();
            if (StringUtils.isBlank(ageRange)) {
                logger.info("项目未配置年龄范围，projectCode: {}", projectElements.getProjectCode());
                return true;
            }

            boolean inRange = checkNumberInRange(ageRange, age, "年龄");
            if (!inRange) {
                logger.warn("年龄不在允许范围内，projectCode: {}, 年龄: {}, 允许范围: {}",
                    projectElements.getProjectCode(), age, ageRange);
            }

            return inRange;
        } catch (Exception e) {
            logger.error("校验年龄范围失败，projectCode: {}", projectElements.getProjectCode(), e);
            return false;
        }
    }

    /**
     * 校验可提现范围
     *
     * @param applyAmount 申请金额
     * @return 是否在允许范围内
     */
    public boolean isAmountInDrawableRange(ProjectInfoDto projectElements, BigDecimal applyAmount) {
        try {
            String drawableAmountRange = projectElements.getElements().getDrawableAmountRange();
            if (StringUtils.isBlank(drawableAmountRange)) {
                logger.info("项目未配置可提现范围，projectCode: {}", projectElements.getProjectCode());
                return true;
            }
            boolean inRange = checkNumberInRange(drawableAmountRange, applyAmount, "金额");
            if (!inRange) {
                logger.warn("申请金额不在可提现范围内，projectCode: {}, 申请金额: {}, 允许范围: {}",
                    projectElements.getProjectCode(), applyAmount, drawableAmountRange);
            }
            return inRange;
        } catch (Exception e) {
            logger.error("校验可提现范围失败，projectCode: {}", projectElements.getProjectCode(), e);
            return false;
        }
    }

    /**
     * 校验时间是否在指定范围内
     *
     * @param timeRange 时间范围，格式：HH:mm-HH:mm
     * @param rangeName 范围名称，用于日志
     * @return 是否在范围内
     */
    private boolean checkTimeInRange(String timeRange, String rangeName) {
        try {
            String[] times = timeRange.split("-");
            if (times.length != 2) {
                logger.warn("{}格式错误，应为HH:mm-HH:mm格式: {}", rangeName, timeRange);
                return false;
            }

            LocalTime startTime = LocalTime.parse(times[0].trim(), DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime endTime = LocalTime.parse(times[1].trim(), DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime currentTime = LocalTime.now();

            boolean inRange;
            if (startTime.isBefore(endTime)) {
                // 同一天内的时间范围
                inRange = !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
            } else {
                // 跨天的时间范围（22:00-06:00）
                inRange = !currentTime.isBefore(startTime) || !currentTime.isAfter(endTime);
            }

            if (inRange) {
                logger.info("当前时间在{}内，时间范围: {}, 当前时间: {}", rangeName, timeRange, currentTime);
            }

            return inRange;
        } catch (DateTimeParseException e) {
            logger.error("解析{}失败: {}", rangeName, timeRange, e);
            return false;
        } catch (Exception e) {
            logger.error("校验{}失败: {}", rangeName, timeRange, e);
            return false;
        }
    }

    /**
     * 校验数值是否在指定范围内
     *
     * @param numberRange 数值范围，格式：min-max
     * @param value       待校验的数值
     * @param valueName   数值名称，用于日志
     * @return 是否在范围内
     */
    private boolean checkNumberInRange(String numberRange, Number value, String valueName) {
        try {
            String[] numbers = numberRange.split("-");
            if (numbers.length != 2) {
                logger.warn("{}格式错误，应为min-max格式: {}", valueName, numberRange);
                return false;
            }
            BigDecimal min = new BigDecimal(numbers[0].trim());
            logger.info("min------->"+min);
            BigDecimal max = new BigDecimal(numbers[1].trim());
            logger.info("max--------->"+max);
            BigDecimal targetValue = new BigDecimal(value.toString());

            boolean inRange = targetValue.compareTo(min) >= 0 && targetValue.compareTo(max) <= 0;

            if (inRange) {
                logger.info("{}在允许范围内，{}: {}, 允许范围: {}", valueName, valueName, value, numberRange);
            }

            return inRange;
        } catch (NumberFormatException e) {
            logger.error("解析{}范围失败: {}", valueName, numberRange, e);
            return false;
        } catch (Exception e) {
            logger.error("校验{}范围失败: {}", valueName, numberRange, e);
            return false;
        }
    }

    /**
     * 执行额度检查、扣减和缓存更新
     */
    private String performDailyCreditCheck(String cacheKey, String lockKey, BigDecimal dayLimit, BigDecimal amount, String businessId, ProjectInfoDto projectInfo){
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ofSeconds(LOCK_WAIT_SECOND), Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.warn("额度校验失败: [{}] 获取额度锁 [{}] 超时。", businessId, lockKey);
                return "授信累计额度已达项目限额上限";
            }
            logger.info("额度检查开始. BusinessId: [{}], Key: [{}], 总限额: [{}]", businessId, cacheKey, dayLimit);

            Object limitAmtObj = cacheService.get(cacheKey);
            BigDecimal remainingAmt;

            if (limitAmtObj == null) {
                logger.info("缓存未命中，从DB加载已用额度. BusinessId: [{}]", businessId);
                BigDecimal usedAmount = calculateUsedDailyCreditFromDB(projectInfo);
                remainingAmt = dayLimit.subtract(usedAmount);
                logger.info("DB已用额度: [{}], 初始剩余额度: [{}]. BusinessId: [{}]", usedAmount, remainingAmt, businessId);
            } else {
                remainingAmt = new BigDecimal(limitAmtObj.toString());
                logger.info("缓存命中，当前Redis剩余额度: [{}]. BusinessId: [{}]", remainingAmt, businessId);
            }

            if (remainingAmt.compareTo(amount) < 0) {
                logger.warn("Redis缓存额度不足，从DB二次确认. BusinessId: [{}], 剩余: [{}], 需要: [{}]", businessId, remainingAmt, amount);
                BigDecimal usedAmountFromDB = calculateUsedDailyCreditFromDB(projectInfo);
                remainingAmt = dayLimit.subtract(usedAmountFromDB);
                logger.info("二次确认：DB最新剩余额度为: [{}]. BusinessId: [{}]", remainingAmt, businessId);

                if (remainingAmt.compareTo(amount) < 0) {
                    logger.warn("额度校验失败: DB二次确认后额度仍不足. BusinessId: [{}], 最终剩余: [{}], 需: [{}]", businessId, remainingAmt, amount);
                    cacheService.put(cacheKey, remainingAmt.max(BigDecimal.ZERO), 24, TimeUnit.HOURS);
                    return "授信累计额度已达项目限额上限";
                }
            }

            BigDecimal newRemainingAmt = remainingAmt.subtract(amount);
            cacheService.put(cacheKey, newRemainingAmt, 24, TimeUnit.HOURS);
            logger.info("额度校验通过: 扣减成功. BusinessId: [{}], 更新后剩余: [{}], 原剩余: [{}], 本次扣减: [{}]", businessId, newRemainingAmt, remainingAmt, amount);

        } catch (InterruptedException e) {
            logger.error("校验日授信限额失败，message:", e);
            return null;
        } finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
        return null;
    }

    /**
     * 构建授信额度校验的Redis Key
     */
    private String buildCreditLimitRedisKey(ProjectInfoDto projectInfo) {
        String projectCode = projectInfo.getProjectCode();
        String today = LocalDate.now().format(DATE_FORMATTER);
        return String.join(":", PREFIX_CREDIT, projectCode, today);
    }

    /**
     * 从DB计算当天已用授信额度
     */
    private BigDecimal calculateUsedDailyCreditFromDB(ProjectInfoDto projectInfo) {
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        return preOrderRepository.sumAmountByDimensionsAndStates(
            projectInfo.getProjectCode(), PreOrderState.AUDIT_PASS, todayStart, todayEnd
        ).orElse(BigDecimal.ZERO);
    }
}
